import type { Member } from "~/src/models/entry/Member";
import { Validations } from "~/src/lib/Validations";
import { PREFECTURE_LIST } from "~~/src/list";
import { replaceHyphens } from "~~/src/lib/utils";

export default class MemberUpdateForm {
    private readonly _prefectures: TList;

    private _name1: string;
    private _name2: string;
    private _name1_hurigana: string;
    private _name2_hurigana: string;
    private _zip_code: string;
    private _tdfk_cd: string;
    private _address1: string;
    private _address2: string;
    private _address3: string;
    private _tel_1: string;
    private _tel_2: string;
    private _tel_3: string;
    private _email: string;
    private _newsletter_opt_in: boolean;

    private _valid: boolean;
    private _errors: { [key: string]: string } = {};

    private _labelkey_map: TJapaneseKeyMap = {
        name1: "姓",
        name2: "名",
        name1_hurigana: "セイ",
        name2_hurigana: "メイ",
        email: "メールアドレス",
        login_pwd: "パスワード",
        newsletter_opt_in: "メルマガの受け取り希望",
        zip_code: "郵便番号",
        tdfk_cd: "都道府県",
        address1: "市区町村・町名",
        address2: "番地（全角）",
        address3: "建物名・部屋番号（全角）",
        tel: "電話番号",
        child_name: "お子さまの名前",
        child_sex: "お子さまの性別",
        child_birthdate: "お子さまの生年月日",
        custom_budget: "ご予算",
        custom_catalog_request_triggers: "カタログ請求のきっかけ（複数回答可）",
        custom_key_points: "特に重視するポイント（複数回答可）",
    };

    constructor(member: Member) {
        this._prefectures = PREFECTURE_LIST; // プルダウン例

        this._name1 = member.name1;
        this._name2 = member.name2;
        this._name1_hurigana = member.name1_hurigana;
        this._name2_hurigana = member.name2_hurigana;
        this._zip_code = member.zip_code;
        this._tdfk_cd = member.tdfk_cd;
        this._address1 = member.address1;
        this._address2 = member.address2;
        this._address3 = member.address3;
        this._tel_1 = member.tel.split("-")[0];
        this._tel_2 = member.tel.split("-")[1];
        this._tel_3 = member.tel.split("-")[2];
        this._email = member.email;
        this._newsletter_opt_in = !member.email_send_ng_flg;

        this._valid = false;
    }
    get valid(): boolean {
        return this._valid;
    }

    set valid(value: boolean) {
        this._valid = value;
    }

    get errors(): { [key: string]: string } {
        return this._errors;
    }

    set errors(value: { [key: string]: string }) {
        this._errors = value;
    }

    get prefectures(): TList {
        return this._prefectures;
    }

    get name1(): string {
        return this._name1;
    }

    set name1(value: string) {
        this._name1 = value;
    }

    get name2(): string {
        return this._name2;
    }

    set name2(value: string) {
        this._name2 = value;
    }

    get name1_hurigana(): string {
        return this._name1_hurigana;
    }

    set name1_hurigana(value: string) {
        this._name1_hurigana = value;
    }

    get name2_hurigana(): string {
        return this._name2_hurigana;
    }

    set name2_hurigana(value: string) {
        this._name2_hurigana = value;
    }

    get name1_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString(this._labelkey_map.name1),
            Validations.maxLength(10, this._labelkey_map.name1),
        ];
    }
    get name2_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString(this._labelkey_map.name2),
            Validations.maxLength(10, this._labelkey_map.name2),
        ];
    }
    get name1_hurigana_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString(this._labelkey_map.name1_hurigana),
            Validations.maxLength(10, this._labelkey_map.name1_hurigana),
        ];
    }
    get name2_hurigana_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString(this._labelkey_map.name2_hurigana),
            Validations.maxLength(10, this._labelkey_map.name2_hurigana),
        ];
    }

    get zip_code(): string {
        return this._zip_code;
    }

    set zip_code(value: string) {
        this._zip_code = value;
    }

    get zip_code_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString(this._labelkey_map.zip_code),
            Validations.zipCode(this._labelkey_map.zip_code),
        ];
    }

    get tdfk_cd(): string {
        return this._tdfk_cd;
    }

    set tdfk_cd(value: string) {
        this._tdfk_cd = value;
    }

    get tdfk_cd_rules(): TValidationFunction[] {
        return [Validations.notEmptyString(this._labelkey_map.tdfk_cd)];
    }

    get address1(): string {
        return this._address1;
    }

    set address1(value: string) {
        this._address1 = value;
    }

    get address2(): string {
        return this._address2;
    }

    set address2(value: string) {
        this._address2 = value;
    }

    get address3(): string {
        return this._address3;
    }

    set address3(value: string) {
        this._address3 = value;
    }

    get address1_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString(this._labelkey_map.address1),
            Validations.maxLength(50, this._labelkey_map.address1),
            Validations.zenkaku(this._labelkey_map.address1),
        ];
    }
    get address2_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString(this._labelkey_map.address2),
            Validations.maxLength(50, this._labelkey_map.address2),
            Validations.zenkaku(this._labelkey_map.address2),
        ];
    }

    get address3_rules(): TValidationFunction[] {
        return [
            Validations.maxLength(50, this._labelkey_map.address3),
            Validations.zenkaku(this._labelkey_map.address3),
        ];
    }

    get tel_1(): string {
        return this._tel_1;
    }

    set tel_1(value: string) {
        this._tel_1 = value;
    }

    get tel_2(): string {
        return this._tel_2;
    }

    set tel_2(value: string) {
        this._tel_2 = value;
    }

    get tel_3(): string {
        return this._tel_3;
    }

    set tel_3(value: string) {
        this._tel_3 = value;
    }

    get tel1_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString(`${this._labelkey_map.tel}１`),
            Validations.tel1(`${this._labelkey_map.tel}１`),
        ];
    }

    get tel2_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString(`${this._labelkey_map.tel}２`),
            Validations.tel2(`${this._labelkey_map.tel}２`),
        ];
    }

    get tel3_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString(`${this._labelkey_map.tel}３`),
            Validations.tel2(`${this._labelkey_map.tel}３`),
        ];
    }

    get email(): string {
        return this._email;
    }

    set email(value: string) {
        this._email = value;
    }

    get email_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString(this._labelkey_map.email),
            Validations.email(this._labelkey_map.email),
        ];
    }

    get newsletter_opt_in(): boolean {
        return this._newsletter_opt_in;
    }

    set newsletter_opt_in(value: boolean) {
        this._newsletter_opt_in = value;
    }

    // エラーメッセージをクリア
    clearError(field: string): void {
        this.errors[field] = "";
    }

    get data(): TMemberUpdate {
        return {
            name1: this.name1,
            name2: this.name2,
            name1_hurigana: this.name1_hurigana,
            name2_hurigana: this.name2_hurigana,
            email: this.email,
            zip_code: this.zip_code,
            tdfk_cd: this.tdfk_cd,
            address1: this.address1,
            address2: replaceHyphens(this.address2),
            address3: replaceHyphens(this.address3),
            tel: `${this.tel_1}-${this.tel_2}-${this.tel_3}`,
            email_send_ng_flg: !this.newsletter_opt_in,
        };
    }
}
