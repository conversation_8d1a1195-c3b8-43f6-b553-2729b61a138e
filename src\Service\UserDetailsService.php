<?php

namespace App\Service;

use App\Enums\EntityFields\ESchoolBagForm;
use App\Kuroko\ApiModel\KurokoApiDynamic\Members;
use App\Kuroko\Entity\IKurokoEntity;
use App\Kuroko\Entity\Member;
use App\Mailer\AppMailer;
use App\Mailer\Sender\ToUser\UserDetailUpdateCompletedSender;
use Cake\Datasource\EntityInterface;
use Cake\Utility\Hash;
use BadMethodCallException;

class UserDetailsService implements IRestService
{
    use ServiceTrait;


    protected array $_defaultConfig = [];

    public function initialize(): void {}

    public function add(array $data = []): EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }

    public function edit(string $id, array $data = []): EntityInterface|null|IKurokoEntity
    {
        $members = new Members();
        /** @var Member $member */
        $member = $this->getIdentity();
        //        debug($member);
        $updatedMember = $members->update($member, $data);
        if (empty($updatedMember->getErrors())) {
            AppMailer::sendToUser((new UserDetailUpdateCompletedSender(
                Hash::get($updatedMember->getJsonData(), ESchoolBagForm::EMAIL->value),
                Hash::get($updatedMember->getJsonData(), ESchoolBagForm::NAME1->value)
            )));
        }
        return $updatedMember;
    }

    public function view(string $id, array $data = []): EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }

    public function index(array $data = []): array|EntityInterface|null|IKurokoEntity
    {
        /** @var Member $member */
        $member = $this->getIdentity();
        return $member;
    }

    public function delete(string $id, array $data = []): bool
    {
        throw new BadMethodCallException();
    }
}
