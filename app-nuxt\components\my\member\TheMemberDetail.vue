<script setup lang="ts">
import { ref, onMounted } from "vue";
import UserDetails from "~/src/models/UserDetails";
import { useRuntimeConfig } from "#app";
import type { Member } from "~/src/models/entry/Member";
const config = useRuntimeConfig();
const emit = defineEmits(["change-password", "member-update"]);

const isLoading = ref(true);
const member = ref<Member>();
const memberUpdateForm = ref<null | Member>(null);

defineProps<{ showUpdateDialog: boolean }>();

// 初期化処理を行う
onMounted(async () => {
    UserDetails.create(config)
        .index()
        .then((m) => {
            if (m) {
                member.value = m;
            } else {
                console.error("member error");
            }
            isLoading.value = false;
        });
});

const dialog = ref(false);
// const openDialog = (member: Member): void => {
//     memberUpdateForm.value = member;
//     dialog.value = true;
// };
// const closeDialog = (): void => {
//     memberUpdateForm.value = null;
//     dialog.value = false;
// };

const gotoChangePassword = (): void => {
    emit("change-password", member.value);
};

const gotoMemberUpdate = (): void => {
    emit("member-update", member.value);
};
</script>

<template>
    <template v-if="isLoading">
        <v-progress-circular indeterminate></v-progress-circular>
    </template>
    <div v-else-if="member">
        <v-list lines="false">
            <v-list-item
                :subtitle="member.name1 + ' ' + member.name2"
                title="お名前[漢字]"
            >
                <!-- <template #prepend>
                    <span class="mr-4 v-list-item-subtitle">お名前[漢字]</span>
                </template> -->
            </v-list-item>
            <v-list-item
                title="お名前[フリガナ]"
                :subtitle="member.name1_hurigana + ' ' + member.name2_hurigana"
            >
                <!-- <template #prepend>
                                    <span class="mr-4 v-list-item-subtitle"
                                        >お名前[フリガナ]</span
                                    >
                                </template> -->
            </v-list-item>
            <v-list-item title="メールアドレス">
                <!-- <template #prepend>
                    <span class="mr-4 v-list-item-subtitle"
                        >メールアドレス</span
                    >
                </template> -->
                <template #subtitle></template>
                <div style="line-height: 1.7">
                    {{ member.email }}
                </div>
            </v-list-item>
            <v-list-item
                title="メルマガの受け取り希望"
                :subtitle="member.email_send_ng_flg ? '希望しない' : '希望する'"
            >
                <!-- <template #prepend>
                    <span class="mr-4 v-list-item-subtitle"
                        >メルマガの受け取り希望</span
                    >
                </template> -->
            </v-list-item>
            <v-list-item :subtitle="'〒 ' + member.zip_code" title="郵便番号">
                <!-- <template #prepend>
                    <span class="mr-4 v-list-item-subtitle">郵便番号</span>
                </template> -->
            </v-list-item>
            <v-list-item
                :subtitle="member.display_tdfk_cd"
                title="住所（都道府県）"
            >
                <!-- <template #prepend>
                    <span class="mr-4 v-list-item-subtitle"
                        >住所（都道府県）</span
                    >
                </template> -->
            </v-list-item>
            <v-list-item :subtitle="member.address1" title="市区町村・町名">
                <!-- <template #prepend>
                    <span class="mr-4 v-list-item-subtitle">市区町村</span>
                </template> -->
            </v-list-item>
            <v-list-item :subtitle="member.address2" title="番地">
                <!-- <template #prepend>
                    <span class="mr-4 v-list-item-subtitle">町名・番地</span>
                </template> -->
            </v-list-item>
            <v-list-item :subtitle="member.address3" title="建物名・部屋番号">
                <!-- <template #prepend>
                    <span class="mr-4 v-list-item-subtitle"
                        >建物名・部屋番号</span
                    >
                </template> -->
            </v-list-item>
            <v-list-item :subtitle="member.tel" title="電話番号">
                <!-- <template #prepend>
                    <span class="mr-4 v-list-item-subtitle">電話番号</span>
                </template> -->
            </v-list-item>
        </v-list>
        <v-row v-if="showUpdateDialog" justify="center">
            <v-col cols="12" md="3">
                <v-btn
                    block
                    color="primary"
                    rounded="xl"
                    size="large"
                    @click="gotoChangePassword()"
                >
                    パスワード変更
                </v-btn>
            </v-col>
            <v-col cols="12" md="3">
                <v-btn
                    block
                    color="primary"
                    rounded="xl"
                    size="large"
                    @click="gotoMemberUpdate()"
                >
                    登録情報変更
                </v-btn>
            </v-col>
        </v-row>
        <!-- <v-dialog
            v-if="showUpdateDialog"
            v-model="dialog"
            max-width="700px"
            @close="closeDialog"
        >
            <the-member-update
                v-if="memberUpdateForm"
                :member-update-form="memberUpdateForm as Member"
            ></the-member-update>
        </v-dialog> -->
    </div>
</template>

<style scoped></style>
