<?php

namespace App\Service;

use App\Enums\EntityFields\EPasswordReminder;
use App\Kuroko\ApiModel\KurokoApiDynamic\Logins;
use App\Kuroko\Entity\IKurokoEntity;
use App\Kuroko\Entity\Member;
use App\Mailer\AppMailer;
use App\Mailer\Sender\ToUser\PasswordResetSender;
use App\Mailer\Sender\ToUser\PasswordResetCompletedSender;
use BadMethodCallException;
use Cake\Datasource\EntityInterface;
use Cake\Utility\Hash;
use Exception;
use PhpParser\Node\Stmt\TryCatch;

class UserPasswordRemindersService implements IRestService
{
    use ServiceTrait;

    protected array $_defaultConfig = [];

    public function add(array $data = []): EntityInterface|null|IKurokoEntity
    {
        try {
            //code...
            $logins = new Logins();
            $response = $logins->reminder($data);

            if (empty($response)) {
                //@todo エラーメッセージをkurokoAPIからとるばあいは要調整
                throw new Exception("password reminder 失敗");
            }

            $extData = json_decode(Hash::get($response, "member.ext_data"));
            if ($extData->status != Member::STATUS_VERIFIED) {
                throw new Exception("本人メール確認未完了");
            }
            if ($extData->maker_id > 0) {
                throw new Exception("クライアントユーザーです");
            }

            AppMailer::sendToUser((new PasswordResetSender(
                Hash::get($response, EPasswordReminder::MEMBER_EMAIL->value),
                Hash::get($response, EPasswordReminder::MEMBER_NAME1->value)
            ))->setViewVars([
                'tmpData' => $response,
            ]));
        } catch (Exception $exception) {
            $this->setErrors([
                '_system' => $exception->getMessage(),
            ]);
        }
        return null;
    }

    public function edit(string $id, array $data = []): EntityInterface|null|IKurokoEntity
    {
        $logins = new Logins();
        $response = $logins->reminder($data);

        AppMailer::sendToUser((new PasswordResetCompletedSender(
            Hash::get($response, EPasswordReminder::MEMBER_EMAIL->value),
            Hash::get($response, EPasswordReminder::MEMBER_NAME1->value)
        ))->setViewVars([
            'tmpData' => $response,
        ]));

        return null;
    }

    public function view(string $id, array $data = []): EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }

    public function index(array $data = []): array|EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }

    public function delete(string $id, array $data = []): bool
    {
        throw new BadMethodCallException();
    }

    public function initialize(): void {}
}
