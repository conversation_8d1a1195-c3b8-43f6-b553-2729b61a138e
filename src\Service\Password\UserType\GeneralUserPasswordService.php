<?php

namespace App\Service\Password\UserType;

use App\Service\Password\IntegratedChangePasswordsService;
use App\Kuroko\Entity\Member;
use App\Mailer\AppMailer;
use App\Mailer\Sender\ToUser\PasswordResetCompletedSender;
use Cake\Utility\Hash;

/**
 * 一般ユーザー専用パスワード変更サービス
 */
class GeneralUserPasswordService extends IntegratedChangePasswordsService
{
    /**
     * パスワード変更完了メール送信
     */
    protected function sendPasswordChangeCompletedEmail(Member $member): void
    {
        try {
            $sender = new PasswordResetCompletedSender(
                $member->getEmail(),
                $member->getName() ?? $member->getEmail()
            );
            
            AppMailer::sendToUser($sender);
        } catch (\Exception $e) {
            \Cake\Log\Log::error("Failed to send password change completed email: " . $e->getMessage());
        }
    }
}
