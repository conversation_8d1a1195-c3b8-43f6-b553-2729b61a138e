<?php

namespace App\Service\Password;

use App\Service\IRestService;
use App\Service\ServiceTrait;
use App\Kuroko\Entity\IKurokoEntity;
use App\Kuroko\Entity\Member;
use App\Authentication\Identifier\Resolver\UserResolver;
use Cake\Datasource\EntityInterface;
use BadMethodCallException;
use Exception;

/**
 * 統合パスワード変更サービス
 * 既存のChangePasswordsServiceを置き換え
 */
abstract class IntegratedChangePasswordsService implements IRestService
{
    use ServiceTrait;

    protected array $_defaultConfig = [];
    private PasswordManagementService $passwordManager;

    public function __construct()
    {
        $this->passwordManager = new PasswordManagementService();
    }

    public function add(array $data = []): EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }

    /**
     * パスワード変更処理
     */
    public function edit(string $id, array $data = []): EntityInterface|null|IKurokoEntity
    {
        try {
            /** @var Member $member */
            $member = $this->getIdentity();
            if (!$member) {
                throw new Exception('認証が必要です');
            }

            $currentPassword = $data['current_password'] ?? '';
            $newPassword = $data['new_password'] ?? '';

            if (empty($currentPassword) || empty($newPassword)) {
                throw new Exception('現在のパスワードと新しいパスワードは必須です');
            }

            // 新システムユーザーの場合
            if ($this->isNewSystemUser($member)) {
                $result = $this->changeNewSystemPassword($member, $currentPassword, $newPassword);
            } else {
                // Kurocoユーザーの場合（フォールバック）
                $result = $this->changeKurocoPassword($member, $data);
            }

            if (!$result) {
                throw new Exception('パスワード変更に失敗しました');
            }

            // 完了メール送信
            $this->sendPasswordChangeCompletedEmail($member);

            return null;
        } catch (Exception $exception) {
            $this->setErrors([
                '_system' => $exception->getMessage(),
            ]);
            return null;
        }
    }

    public function view(string $id, array $data = []): EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }

    public function index(array $data = []): array|EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }

    public function delete(string $id, array $data = []): bool
    {
        throw new BadMethodCallException();
    }

    public function initialize(): void {}

    /**
     * 新システムユーザーかどうかを判定
     */
    private function isNewSystemUser(Member $member): bool
    {
        // Memberエンティティから新システムユーザーかどうかを判定
        // 実装は認証システムの構造に依存
        return false; // 暫定実装
    }

    /**
     * 新システムユーザーのパスワード変更
     */
    private function changeNewSystemPassword(Member $member, string $currentPassword, string $newPassword): bool
    {
        try {
            // 現在のパスワードを検証
            $userType = $this->getUserTypeFromMember($member);
            $user = $this->findUserByEmail($member->getEmail(), $userType);
            
            if (!$user) {
                return false;
            }

            // 現在のパスワードを検証
            $passwordHasher = new \Cake\Auth\DefaultPasswordHasher();
            if (!$passwordHasher->check($currentPassword, $user->password)) {
                throw new Exception('現在のパスワードが正しくありません');
            }

            // 新しいパスワードをバリデーション
            $authService = new \App\Service\AuthenticationService();
            $passwordErrors = $authService->validatePassword($newPassword);
            if (!empty($passwordErrors)) {
                throw new Exception('新しいパスワードが要件を満たしていません: ' . implode(', ', $passwordErrors));
            }

            // パスワードを更新
            $user->password = $newPassword; // エンティティで自動ハッシュ化
            $userTable = $this->getUserTable($userType);
            
            return $userTable->save($user) !== false;
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * Kurocoユーザーのパスワード変更（フォールバック）
     */
    private function changeKurocoPassword(Member $member, array $data): bool
    {
        try {
            // 既存のKuroco API呼び出し処理
            $logins = new \App\Kuroko\ApiModel\KurokoApiDynamic\Logins();
            $response = $logins->resetPassword($member, $data);
            
            return $response === "";
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Memberエンティティからユーザータイプを判定
     */
    private function getUserTypeFromMember(Member $member): string
    {
        // グループIDからユーザータイプを判定
        if ($member->isBelongsToSwbGroup()) {
            return PasswordManagementService::USER_TYPE_SWB;
        } elseif ($member->isBelongsToClientGroup()) {
            return PasswordManagementService::USER_TYPE_MAKER;
        }
        
        return PasswordManagementService::USER_TYPE_GENERAL;
    }

    /**
     * メールアドレスでユーザーを検索
     */
    private function findUserByEmail(string $email, string $userType)
    {
        $tableRegistry = \Cake\ORM\TableRegistry::getTableLocator();
        
        switch ($userType) {
            case PasswordManagementService::USER_TYPE_GENERAL:
                return $tableRegistry->get('GeneralUsers')->find()->where(['email' => $email])->first();
            case PasswordManagementService::USER_TYPE_SWB:
                return $tableRegistry->get('SwbUsers')->find()->where(['email' => $email])->first();
            case PasswordManagementService::USER_TYPE_MAKER:
                return $tableRegistry->get('MakerUsers')->find()->where(['email' => $email])->first();
            default:
                return null;
        }
    }

    /**
     * ユーザータイプに対応するテーブルを取得
     */
    private function getUserTable(string $userType)
    {
        $tableRegistry = \Cake\ORM\TableRegistry::getTableLocator();
        
        switch ($userType) {
            case PasswordManagementService::USER_TYPE_GENERAL:
                return $tableRegistry->get('GeneralUsers');
            case PasswordManagementService::USER_TYPE_SWB:
                return $tableRegistry->get('SwbUsers');
            case PasswordManagementService::USER_TYPE_MAKER:
                return $tableRegistry->get('MakerUsers');
            default:
                throw new Exception("Invalid user type: {$userType}");
        }
    }

    /**
     * パスワード変更完了メール送信（抽象メソッド）
     */
    abstract protected function sendPasswordChangeCompletedEmail(Member $member): void;
}
